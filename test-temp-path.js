// Test script to check temp directory paths
const { app } = require('electron');
const path = require('path');
const os = require('os');

function getTransactionTempDir() {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  console.log('Environment:', {
    NODE_ENV: process.env.NODE_ENV,
    APP_ENV: process.env.APP_ENV,
    isDevelopment
  });
  
  if (isDevelopment) {
    const devPath = path.join(process.cwd(), 'temp', 'transactions');
    console.log('Development path:', devPath);
    return devPath;
  } else {
    try {
      const userDataPath = app.getPath('userData');
      const prodPath = path.join(userDataPath, 'temp', 'transactions');
      console.log('Production path:', prodPath);
      return prodPath;
    } catch (error) {
      const fallbackPath = path.join(os.tmpdir(), 'electron-app-transactions');
      console.log('Fallback path:', fallbackPath);
      return fallbackPath;
    }
  }
}

console.log('Current working directory:', process.cwd());
console.log('Temp directory would be:', getTransactionTempDir());
