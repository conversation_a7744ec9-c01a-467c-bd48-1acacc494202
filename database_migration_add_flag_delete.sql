-- Migration: Add flag_delete column to all tables
-- Date: 2025-07-23
-- Description: Adds flag_delete column with default value 0 to all existing tables for soft delete functionality

-- Start transaction
BEGIN;

-- Log migration start
SELECT 'Starting migration to add flag_delete column to all tables' as migration_status;

-- 1. company_setting table
ALTER TABLE company_setting 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN company_setting.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 2. log_history_user table
ALTER TABLE log_history_user 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN log_history_user.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 3. merchant table
ALTER TABLE merchant 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN merchant.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 4. merchant_bank table
ALTER TABLE merchant_bank 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN merchant_bank.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 5. merchant_unionpay table
ALTER TABLE merchant_unionpay 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN merchant_unionpay.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 6. merchant_wechat table
ALTER TABLE merchant_wechat 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN merchant_wechat.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 7. network_service table
ALTER TABLE network_service 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN network_service.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 8. tmas_user_role table
ALTER TABLE tmas_user_role 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN tmas_user_role.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 9. tmst_bank table
ALTER TABLE tmst_bank 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN tmst_bank.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 10. tmst_category table
ALTER TABLE tmst_category 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN tmst_category.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 11. tmst_group table
ALTER TABLE tmst_group 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN tmst_group.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 12. tmst_product table
ALTER TABLE tmst_product 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN tmst_product.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 13. tmst_typelist table
ALTER TABLE tmst_typelist 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN tmst_typelist.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 14. tmst_zone table
ALTER TABLE tmst_zone 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN tmst_zone.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 15. transaction_duplicate_config table
ALTER TABLE transaction_duplicate_config 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN transaction_duplicate_config.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 16. transaction_duplicate_log table
ALTER TABLE transaction_duplicate_log 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN transaction_duplicate_log.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 17. transaction_e_pos table
ALTER TABLE transaction_e_pos 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN transaction_e_pos.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 18. transaction_summary_report_detail table
ALTER TABLE transaction_summary_report_detail 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN transaction_summary_report_detail.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- 19. tsys_user table
ALTER TABLE tsys_user 
ADD COLUMN flag_delete INTEGER NOT NULL DEFAULT 0;

COMMENT ON COLUMN tsys_user.flag_delete IS 'Soft delete flag: 0 = active, 1 = deleted';

-- Add check constraints to ensure flag_delete is only 0 or 1
ALTER TABLE company_setting ADD CONSTRAINT chk_company_setting_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE log_history_user ADD CONSTRAINT chk_log_history_user_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE merchant ADD CONSTRAINT chk_merchant_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE merchant_bank ADD CONSTRAINT chk_merchant_bank_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE merchant_unionpay ADD CONSTRAINT chk_merchant_unionpay_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE merchant_wechat ADD CONSTRAINT chk_merchant_wechat_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE network_service ADD CONSTRAINT chk_network_service_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE tmas_user_role ADD CONSTRAINT chk_tmas_user_role_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE tmst_bank ADD CONSTRAINT chk_tmst_bank_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE tmst_category ADD CONSTRAINT chk_tmst_category_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE tmst_group ADD CONSTRAINT chk_tmst_group_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE tmst_product ADD CONSTRAINT chk_tmst_product_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE tmst_typelist ADD CONSTRAINT chk_tmst_typelist_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE tmst_zone ADD CONSTRAINT chk_tmst_zone_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE transaction_duplicate_config ADD CONSTRAINT chk_transaction_duplicate_config_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE transaction_duplicate_log ADD CONSTRAINT chk_transaction_duplicate_log_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE transaction_e_pos ADD CONSTRAINT chk_transaction_e_pos_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE transaction_summary_report_detail ADD CONSTRAINT chk_transaction_summary_report_detail_flag_delete CHECK (flag_delete IN (0, 1));
ALTER TABLE tsys_user ADD CONSTRAINT chk_tsys_user_flag_delete CHECK (flag_delete IN (0, 1));

-- Create indexes for flag_delete columns for better query performance
CREATE INDEX IF NOT EXISTS idx_company_setting_flag_delete ON company_setting(flag_delete);
CREATE INDEX IF NOT EXISTS idx_log_history_user_flag_delete ON log_history_user(flag_delete);
CREATE INDEX IF NOT EXISTS idx_merchant_flag_delete ON merchant(flag_delete);
CREATE INDEX IF NOT EXISTS idx_merchant_bank_flag_delete ON merchant_bank(flag_delete);
CREATE INDEX IF NOT EXISTS idx_merchant_unionpay_flag_delete ON merchant_unionpay(flag_delete);
CREATE INDEX IF NOT EXISTS idx_merchant_wechat_flag_delete ON merchant_wechat(flag_delete);
CREATE INDEX IF NOT EXISTS idx_network_service_flag_delete ON network_service(flag_delete);
CREATE INDEX IF NOT EXISTS idx_tmas_user_role_flag_delete ON tmas_user_role(flag_delete);
CREATE INDEX IF NOT EXISTS idx_tmst_bank_flag_delete ON tmst_bank(flag_delete);
CREATE INDEX IF NOT EXISTS idx_tmst_category_flag_delete ON tmst_category(flag_delete);
CREATE INDEX IF NOT EXISTS idx_tmst_group_flag_delete ON tmst_group(flag_delete);
CREATE INDEX IF NOT EXISTS idx_tmst_product_flag_delete ON tmst_product(flag_delete);
CREATE INDEX IF NOT EXISTS idx_tmst_typelist_flag_delete ON tmst_typelist(flag_delete);
CREATE INDEX IF NOT EXISTS idx_tmst_zone_flag_delete ON tmst_zone(flag_delete);
CREATE INDEX IF NOT EXISTS idx_transaction_duplicate_config_flag_delete ON transaction_duplicate_config(flag_delete);
CREATE INDEX IF NOT EXISTS idx_transaction_duplicate_log_flag_delete ON transaction_duplicate_log(flag_delete);
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_flag_delete ON transaction_e_pos(flag_delete);
CREATE INDEX IF NOT EXISTS idx_transaction_summary_report_detail_flag_delete ON transaction_summary_report_detail(flag_delete);
CREATE INDEX IF NOT EXISTS idx_tsys_user_flag_delete ON tsys_user(flag_delete);

-- Commit transaction
COMMIT;

-- Verification queries
SELECT 'Migration completed successfully - flag_delete column added to all tables' as migration_status;

-- Show summary of changes
SELECT 
    'Summary: Added flag_delete column to 19 tables with default value 0' as summary,
    'All existing records will have flag_delete = 0 (active)' as note,
    'Use flag_delete = 1 to mark records as deleted' as usage;
