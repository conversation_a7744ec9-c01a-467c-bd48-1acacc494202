const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Database connection string
const DB_CONNECTION = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";

async function runFlagDeleteMigration() {
  const client = new Client({
    connectionString: DB_CONNECTION
  });

  try {
    await client.connect();
    console.log('🚀 Starting flag_delete column migration...\n');

    // Check if migration file exists
    const migrationPath = path.join(__dirname, 'database_migration_add_flag_delete.sql');
    if (!fs.existsSync(migrationPath)) {
      throw new Error('Migration file not found: database_migration_add_flag_delete.sql');
    }

    // Read migration SQL
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Migration file loaded successfully');

    // Check current state before migration
    console.log('\n🔍 Checking current state before migration...');
    
    const beforeCheck = await client.query(`
      SELECT 
        table_name,
        EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_name = t.table_name 
            AND column_name = 'flag_delete'
            AND table_schema = 'public'
        ) as has_flag_delete
      FROM information_schema.tables t
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `);

    const tablesWithFlagDelete = beforeCheck.rows.filter(row => row.has_flag_delete).length;
    const tablesWithoutFlagDelete = beforeCheck.rows.filter(row => !row.has_flag_delete).length;

    console.log(`📊 Before migration:`);
    console.log(`   - Total tables: ${beforeCheck.rows.length}`);
    console.log(`   - Tables with flag_delete: ${tablesWithFlagDelete}`);
    console.log(`   - Tables without flag_delete: ${tablesWithoutFlagDelete}`);

    if (tablesWithoutFlagDelete === 0) {
      console.log('\n✅ All tables already have flag_delete column. Migration not needed.');
      return;
    }

    // Run migration
    console.log('\n🔧 Running migration...');
    await client.query(migrationSQL);
    console.log('✅ Migration executed successfully');

    // Verify migration results
    console.log('\n🔍 Verifying migration results...');
    
    const afterCheck = await client.query(`
      SELECT 
        table_name,
        EXISTS (
          SELECT 1 FROM information_schema.columns 
          WHERE table_name = t.table_name 
            AND column_name = 'flag_delete'
            AND table_schema = 'public'
        ) as has_flag_delete,
        (
          SELECT column_default 
          FROM information_schema.columns 
          WHERE table_name = t.table_name 
            AND column_name = 'flag_delete'
            AND table_schema = 'public'
        ) as flag_delete_default
      FROM information_schema.tables t
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `);

    const afterTablesWithFlagDelete = afterCheck.rows.filter(row => row.has_flag_delete).length;
    const afterTablesWithoutFlagDelete = afterCheck.rows.filter(row => !row.has_flag_delete).length;

    console.log(`📊 After migration:`);
    console.log(`   - Total tables: ${afterCheck.rows.length}`);
    console.log(`   - Tables with flag_delete: ${afterTablesWithFlagDelete}`);
    console.log(`   - Tables without flag_delete: ${afterTablesWithoutFlagDelete}`);

    if (afterTablesWithoutFlagDelete === 0) {
      console.log('\n✅ SUCCESS: All tables now have flag_delete column!');
    } else {
      console.log('\n❌ WARNING: Some tables still missing flag_delete column:');
      afterCheck.rows.filter(row => !row.has_flag_delete).forEach(row => {
        console.log(`   - ${row.table_name}`);
      });
    }

    // Show detailed results
    console.log('\n📋 Detailed results:');
    afterCheck.rows.forEach(row => {
      const status = row.has_flag_delete ? '✅' : '❌';
      const defaultValue = row.flag_delete_default || 'N/A';
      console.log(`   ${status} ${row.table_name} (default: ${defaultValue})`);
    });

    // Test a few sample queries to verify the column works
    console.log('\n🧪 Testing flag_delete column functionality...');
    
    // Test on merchant table (if it exists)
    try {
      const testResult = await client.query(`
        SELECT COUNT(*) as total_records, 
               COUNT(CASE WHEN flag_delete = 0 THEN 1 END) as active_records,
               COUNT(CASE WHEN flag_delete = 1 THEN 1 END) as deleted_records
        FROM merchant;
      `);
      
      console.log('📊 Merchant table test:');
      console.log(`   - Total records: ${testResult.rows[0].total_records}`);
      console.log(`   - Active records (flag_delete=0): ${testResult.rows[0].active_records}`);
      console.log(`   - Deleted records (flag_delete=1): ${testResult.rows[0].deleted_records}`);
    } catch (error) {
      console.log('⚠️ Could not test merchant table:', error.message);
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📝 Usage notes:');
    console.log('   - Use flag_delete = 0 for active records (default)');
    console.log('   - Use flag_delete = 1 to mark records as deleted');
    console.log('   - Add WHERE flag_delete = 0 to queries to exclude deleted records');
    console.log('   - All existing records have been set to flag_delete = 0');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the migration
runFlagDeleteMigration()
  .then(() => {
    console.log('\n✅ Flag delete migration completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Flag delete migration failed:', error);
    process.exit(1);
  });
