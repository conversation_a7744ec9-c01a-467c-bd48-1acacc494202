const { Client } = require('pg');

// Database connection string
const DB_CONNECTION = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";

async function checkAllTables() {
  const client = new Client({
    connectionString: DB_CONNECTION
  });

  try {
    await client.connect();
    console.log('🔍 Checking all tables in the database...\n');

    // Get all user tables (excluding system tables)
    const tablesResult = await client.query(`
      SELECT 
        table_name,
        table_type
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `);

    console.log(`📊 Found ${tablesResult.rows.length} tables:\n`);
    
    const allTables = [];
    
    for (const table of tablesResult.rows) {
      const tableName = table.table_name;
      console.log(`🔍 Analyzing table: ${tableName}`);
      
      // Get column information for each table
      const columnsResult = await client.query(`
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length
        FROM information_schema.columns 
        WHERE table_name = $1 
          AND table_schema = 'public'
        ORDER BY ordinal_position;
      `, [tableName]);

      // Check if flag_delete column already exists
      const hasFlagDelete = columnsResult.rows.some(col => col.column_name === 'flag_delete');
      
      const tableInfo = {
        name: tableName,
        columnCount: columnsResult.rows.length,
        hasFlagDelete: hasFlagDelete,
        columns: columnsResult.rows.map(col => ({
          name: col.column_name,
          type: col.data_type,
          nullable: col.is_nullable === 'YES',
          default: col.column_default,
          maxLength: col.character_maximum_length
        }))
      };
      
      allTables.push(tableInfo);
      
      console.log(`   - Columns: ${tableInfo.columnCount}`);
      console.log(`   - Has flag_delete: ${hasFlagDelete ? '✅ YES' : '❌ NO'}`);
      
      // Show some key columns
      const keyColumns = columnsResult.rows
        .filter(col => ['id', 'active', 'create_by', 'create_dt', 'update_by', 'update_dt'].includes(col.column_name))
        .map(col => col.column_name);
      
      if (keyColumns.length > 0) {
        console.log(`   - Key columns: ${keyColumns.join(', ')}`);
      }
      console.log('');
    }

    // Summary
    console.log('\n📋 SUMMARY:');
    console.log(`Total tables: ${allTables.length}`);
    
    const tablesWithFlagDelete = allTables.filter(t => t.hasFlagDelete);
    const tablesWithoutFlagDelete = allTables.filter(t => !t.hasFlagDelete);
    
    console.log(`Tables with flag_delete: ${tablesWithFlagDelete.length}`);
    console.log(`Tables without flag_delete: ${tablesWithoutFlagDelete.length}`);
    
    if (tablesWithFlagDelete.length > 0) {
      console.log('\n✅ Tables that already have flag_delete:');
      tablesWithFlagDelete.forEach(table => {
        console.log(`   - ${table.name}`);
      });
    }
    
    if (tablesWithoutFlagDelete.length > 0) {
      console.log('\n❌ Tables that need flag_delete column:');
      tablesWithoutFlagDelete.forEach(table => {
        console.log(`   - ${table.name} (${table.columnCount} columns)`);
      });
    }

    // Generate the list of tables that need the column
    console.log('\n🔧 Tables to modify:');
    const tablesToModify = tablesWithoutFlagDelete.map(t => t.name);
    console.log(JSON.stringify(tablesToModify, null, 2));

    return {
      allTables,
      tablesWithFlagDelete,
      tablesWithoutFlagDelete,
      tablesToModify
    };

  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the check
checkAllTables()
  .then(result => {
    console.log('\n✅ Table analysis completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Failed to analyze tables:', error);
    process.exit(1);
  });
