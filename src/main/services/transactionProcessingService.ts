import <PERSON> from 'papa<PERSON><PERSON>';
import fs from 'fs';
import path from 'path';
import { executeQuery } from '../db';

interface TransactionRecord {
  id?: number;
  reference_no?: string;
  transaction_id: string;
  transaction_out_id?: string;
  transaction_card_no?: string;
  transaction_merchant_id?: string;
  transaction_merchant_name?: string;
  transaction_merchant_vat?: string;
  transaction_time: Date;
  transaction_amount: number;
  transaction_refund_id?: string;
  transaction_refund_out_id?: string;
  transaction_mch_id?: string;
  transaction_sub_mch_id?: string;
  transaction_trade_type?: string;
  transaction_trade_status?: string;
  transaction_bank_type?: string;
  transaction_fee_type?: string;
  transaction_coupon_amount?: number;
  transaction_file_name: string;
  transaction_file_name_backup?: string;
  transaction_channel_type?: string;
  create_by?: string;
  create_dt?: Date;
  update_by?: string;
  update_dt?: Date;
}

interface ProcessingResult {
  success: boolean;
  totalRows: number;
  processedRows: number;
  skippedRows: number;
  errorRows: number;
  errors: string[];
  transactions: TransactionRecord[];
}

interface DuplicateHandlingOptions {
  strategy: 'SKIP' | 'UPDATE' | 'LOG_ONLY' | 'MERGE' | 'INSERT_ALL';
  logDuplicates: boolean;
  updateFields?: string[];
}

interface DuplicateLogEntry {
  transaction_id: string;
  original_file: string;
  duplicate_file: string;
  original_amount: number;
  duplicate_amount: number;
  action_taken: string;
  detected_at: Date;
}

interface MerchantInfo {
  merchant_id: string;
  merchant_name: string;
  merchant_vat: string;
}

export class TransactionProcessingService {
  
  /**
   * Process CSV/Excel file and extract transaction data
   */
  async processTransactionFile(
    filePath: string, 
    fileName: string, 
    backupPath: string,
    currentUser: string = 'SYSTEM'
  ): Promise<ProcessingResult> {
    const result: ProcessingResult = {
      success: false,
      totalRows: 0,
      processedRows: 0,
      skippedRows: 0,
      errorRows: 0,
      errors: [],
      transactions: []
    };

    try {
      console.log(`📄 Processing transaction file: ${fileName}`);
      
      // Check if file exists before reading
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }
      
      // Read file content
      const fileContent = fs.readFileSync(filePath, 'utf8');
      
      // Try different CSV parsing configurations
      let parseResult;
      let rows: string[][];

      console.log('🔍 File content preview (first 500 chars):');
      console.log(fileContent.substring(0, 500));

      // First try with backticks (WeChat format) - this is the most likely format
      parseResult = Papa.parse(fileContent, {
        header: false,
        skipEmptyLines: true,
        delimiter: ',',
        quoteChar: '`',
        escapeChar: '`'  // Use backtick as escape character too
      });

      rows = parseResult.data as string[][];
      console.log('🔍 Backtick parsing attempt - errors:', parseResult.errors.length);
      console.log('🔍 Backtick parsing attempt - first row length:', rows[0]?.length);
      console.log('🔍 Backtick parsing attempt - first row preview:', rows[0]?.slice(0, 5));

      // If backtick parsing has too many errors, try manual parsing
      if (parseResult.errors.length > 10 || (rows.length > 0 && rows[0].length < 10)) {
        console.log('🔄 Backtick parsing failed, trying manual parsing...');

        // Manual parsing for WeChat backtick-quoted CSV
        const lines = fileContent.split('\n').filter(line => line.trim());
        rows = [];

        for (const line of lines) {
          let fields = [];

          // Remove BOM if present
          const cleanLine = line.replace(/^\uFEFF/, '');

          // Check if this is a header row (uses regular commas, no backticks)
          const isHeaderRow = cleanLine.includes('Transaction time') || cleanLine.includes('Official account ID');

          if (isHeaderRow) {
            // Parse header row with regular comma separation
            fields = cleanLine.split(',').map(field => field.trim());
            console.log(`🔍 Parsed header row with ${fields.length} fields`);
          } else {
            // Parse data row with comma-backtick separation
            const parts = cleanLine.split(',`');

            for (let i = 0; i < parts.length; i++) {
              let field = parts[i];

              // Remove leading backtick from first field
              if (i === 0 && field.startsWith('`')) {
                field = field.substring(1);
              }

              // Remove trailing backtick from last field
              if (i === parts.length - 1 && field.endsWith('`')) {
                field = field.substring(0, field.length - 1);
              }

              // Clean up the field
              field = field.trim().replace(/\r$/, '');
              fields.push(field);
            }
          }

          // Add all rows (header and data) - we'll filter later
          if (fields.length > 5) {
            rows.push(fields);
          }
        }

        console.log('🔍 Manual parsing - rows:', rows.length);
        console.log('🔍 Manual parsing - first row length:', rows[0]?.length);
        console.log('🔍 Manual parsing - first row preview:', rows[0]?.slice(0, 5));

        // Clear parse errors since we're using manual parsing
        parseResult.errors = [];
      }

      // If all parsing attempts failed, try manual line splitting as last resort
      if (parseResult.errors.length > 0 || (rows.length > 0 && rows[0].length < 10)) {
        console.log('🔄 All CSV parsing failed, trying manual line splitting...');
        const lines = fileContent.split('\n').filter(line => line.trim());
        rows = lines.map(line => line.split(',').map(cell => cell.trim()));
        console.log('🔍 Manual parsing - rows:', rows.length);
        console.log('🔍 Manual parsing - first row length:', rows[0]?.length);
      }

      if (parseResult.errors.length > 0) {
        console.log('⚠️ CSV parsing had errors, but continuing with available data');
        // Don't add errors to result if we have usable data
        if (rows.length === 0 || (rows.length > 0 && rows[0].length < 5)) {
          result.errors.push(`CSV parsing errors: ${parseResult.errors.map(e => e.message).join(', ')}`);
        }
      }
      result.totalRows = rows.length;

      console.log(`📊 Found ${rows.length} rows in file`);

      // Debug: Log first few rows and last few rows to understand the format
      console.log('🔍 Debug - First 3 rows:');
      for (let i = 0; i < Math.min(3, rows.length); i++) {
        console.log(`Row ${i}:`, rows[i]);
        console.log(`Row ${i} length:`, rows[i]?.length);
      }

      console.log('🔍 Debug - Last 3 rows:');
      for (let i = Math.max(0, rows.length - 3); i < rows.length; i++) {
        console.log(`Row ${i}:`, rows[i]);
        console.log(`Row ${i} length:`, rows[i]?.length);
        console.log(`Row ${i} first column:`, rows[i]?.[0]);
      }

      // Intelligently find and skip header row
      let headerRowIndex = -1;

      // Look for the header row by checking for "Transaction time" in the first column
      for (let i = 0; i < Math.min(3, rows.length); i++) {
        const firstCol = rows[i]?.[0]?.replace(/`/g, '').trim();
        // Remove BOM character if present
        const cleanFirstCol = firstCol?.replace(/^\uFEFF/, '');

        if (cleanFirstCol?.includes('Transaction time') || cleanFirstCol?.includes('Official account ID')) {
          headerRowIndex = i;
          console.log(`🔍 Found header row at index ${i}: "${cleanFirstCol}"`);
          break;
        }
      }

      // If no header found, assume first row is header (fallback)
      if (headerRowIndex === -1) {
        headerRowIndex = 0;
        console.log('⚠️ No clear header found, assuming first row is header');
      }

      // Skip header row and summary rows
      const dataRows = rows.slice(headerRowIndex + 1).filter((row, index) => {
        const rowNumber = index + headerRowIndex + 2; // Adjust for header position

        // Skip empty rows
        if (!row || row.length === 0 || row[0]?.trim() === '') {
          console.log(`🔍 Skipping empty row ${rowNumber}`);
          result.skippedRows++;
          return false;
        }

        // Skip summary rows - check for "Total" in first column or if it's a summary row pattern
        const firstCol = row[0]?.replace(/`/g, '').trim();
        if (firstCol?.includes('Total') || firstCol === 'Total transaction count') {
          console.log(`🔍 Skipping summary row ${rowNumber}: "${firstCol}"`);
          result.skippedRows++;
          return false;
        }

        // Skip rows that look like summary data (numeric values in first column without proper transaction time format)
        // Valid transaction rows should start with a date/time pattern like "2025-07-14 12:40:23"
        if (firstCol && /^\d+\.?\d*$/.test(firstCol)) {
          // This is likely a summary row with just numbers
          console.log(`🔍 Skipping numeric summary row ${rowNumber}: "${firstCol}"`);
          result.skippedRows++;
          return false;
        }

        // Skip rows with too few columns (should have at least 17 columns for basic transaction data)
        if (row.length < 17) {
          console.log(`🔍 Skipping row ${rowNumber} with too few columns (${row.length}): ${row.slice(0, 3)}`);
          result.skippedRows++;
          return false;
        }

        console.log(`✅ Including data row ${rowNumber}: "${firstCol}" (${row.length} columns)`);
        return true;
      });

      console.log(`📋 Processing ${dataRows.length} data rows`);

      // Determine channel type from filename
      const channelType = this.determineChannelType(fileName);

      // First pass: Parse all transactions without validation
      const allTransactions: TransactionRecord[] = [];
      for (let i = 0; i < dataRows.length; i++) {
        try {
          const row = dataRows[i];
          console.log(`🔍 Processing row ${i + 2}:`, row);
          console.log(`🔍 Row length: ${row.length}, First column: "${row[0]}"`);

          const transaction = await this.parseTransactionRow(row, fileName, backupPath, channelType, currentUser, false); // Skip validation in first pass

          if (transaction) {
            allTransactions.push(transaction);
          }
        } catch (error) {
          result.errorRows++;
          result.errors.push(`Row ${i + 2}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          console.error(`❌ Error processing row ${i + 2}:`, error);
        }
      }

      // Second pass: Validate refund transactions against success transactions in the same file
      console.log(`🔍 Second pass: Validating ${allTransactions.length} transactions`);
      for (const transaction of allTransactions) {
        if (transaction.transaction_trade_status === 'refund') {
          const hasMatchingSuccess = this.validateRefundTransactionInBatch(transaction.transaction_id, allTransactions);
          if (!hasMatchingSuccess) {
            console.log(`⚠️ REFUND VALIDATION FAILED: No matching SUCCESS transaction found in same file for transaction_id: ${transaction.transaction_id} - SKIPPING`);
            result.skippedRows++;
            continue;
          }
          console.log(`✅ REFUND VALIDATION PASSED: Found matching SUCCESS transaction in same file for transaction_id: ${transaction.transaction_id}`);
        }

        result.transactions.push(transaction);
        result.processedRows++;
      }

      result.success = result.errorRows === 0 || result.processedRows > 0;
      
      console.log(`✅ Processing complete: ${result.processedRows} processed, ${result.errorRows} errors, ${result.skippedRows} skipped`);
      
      return result;

    } catch (error) {
      console.error('❌ Error processing transaction file:', error);
      result.errors.push(`File processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Parse individual transaction row based on column mapping
   */
  private async parseTransactionRow(
    row: string[],
    fileName: string,
    backupPath: string,
    channelType: string,
    currentUser: string,
    skipValidation: boolean = false
  ): Promise<TransactionRecord | null> {
    try {
      // Column mapping based on the CSV template:
      // 0: Transaction time
      // 1: Official account ID(appid) 
      // 2: Vendor ID(mch_id)
      // 3: Sub vendor ID(sub_mch_id) 
      // 4: Device ID(Device_info)
      // 5: Wechat order number(transaction_id)
      // 6: Vendor order number(out_transaction_id)
      // 7: User tag(openid)
      // 8: Transaction type(trade_type)
      // 9: Transaction status(trade_state)
      // 10: Payment bank(bank_type)
      // 11: Currency type(fee_type)
      // 12: Total amount(total_fee)
      // 13: Coupon amount
      // 14: Wechat refund number(refund_id)
      // 15: Vendor refund number(out_refund_no)
      // 16: Refund amount(refund_fee)


      // Parse transaction time - extract just the datetime part if it contains extra data
      let transactionTimeStr = row[0]?.replace(/`/g, '').trim();
      console.log(`🔍 Parsing transaction time from: "${transactionTimeStr}"`);

      // If the field contains a comma, extract only the part before the comma (the actual datetime)
      if (transactionTimeStr.includes(',')) {
        transactionTimeStr = transactionTimeStr.split(',')[0].trim();
        console.log(`🔍 Extracted datetime part: "${transactionTimeStr}"`);
      }

      // Try different date parsing approaches
      let transactionTime: Date;

      // First try direct parsing
      transactionTime = new Date(transactionTimeStr);
      console.log(`🔍 Direct parsing result: ${transactionTime.toISOString()}, valid: ${!isNaN(transactionTime.getTime())}`);

      if (isNaN(transactionTime.getTime())) {
        // Try alternative date formats
        const altFormats = [
          transactionTimeStr.replace(/(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})/, '$1T$2'),
          transactionTimeStr.replace(/(\d{4}\/\d{2}\/\d{2})\s+(\d{2}:\d{2}:\d{2})/, '$1T$2'),
          transactionTimeStr + 'Z', // Add UTC timezone
          transactionTimeStr.replace(' ', 'T') + '+07:00', // Add Thailand timezone
        ];

        let parsedDate = null;
        for (const format of altFormats) {
          console.log(`🔍 Trying format: "${format}"`);
          const testDate = new Date(format);
          if (!isNaN(testDate.getTime())) {
            parsedDate = testDate;
            console.log(`✅ Successfully parsed with format: ${format} -> ${testDate.toISOString()}`);
            break;
          }
        }

        if (!parsedDate) {
          throw new Error(`Invalid transaction time: ${transactionTimeStr}`);
        }

        transactionTime = parsedDate;
      } else {
        console.log(`✅ Direct parsing successful: ${transactionTime.toISOString()}`);
      }

      // Get transaction IDs
      const transactionId = row[5]?.replace(/`/g, '').trim();
      const transactionOutId = row[6]?.replace(/`/g, '').trim();
      
      if (!transactionId) {
        throw new Error('Missing transaction ID');
      }

      // Get merchant info from sub_mch_id lookup
      const subMchId = row[3]?.replace(/`/g, '').trim();
      const merchantInfo = await this.getMerchantInfo(subMchId);

      // Parse transaction status and amount
      const rawTradeStatus = row[9]?.replace(/`/g, '').trim();
      const tradeStatus = rawTradeStatus.toLowerCase();
      let transactionAmount = 0;

      // Debug logging for trade status
      console.log(`🔍 Trade Status Debug - Row[9]: "${rawTradeStatus}" -> Processed: "${tradeStatus}"`);

      if (tradeStatus === 'success') {
        // For successful transactions, use total_fee (column 12)
        const amountStr = row[12]?.replace(/`/g, '').trim();
        transactionAmount = parseFloat(amountStr) || 0;
        console.log(`✅ SUCCESS transaction - Amount from column 12: ${amountStr} -> ${transactionAmount}`);
      } else if (tradeStatus === 'refund') {
        // For refunds, use refund_fee (column 16) as negative
        const refundAmountStr = row[16]?.replace(/`/g, '').trim();
        transactionAmount = -(parseFloat(refundAmountStr) || 0);
        console.log(`🔄 REFUND transaction - Amount from column 16: ${refundAmountStr} -> ${transactionAmount}`);
      } else {
        console.log(`⚠️ Unknown trade status: "${tradeStatus}" - Setting amount to 0`);
      }

      // Parse coupon amount
      const couponAmountStr = row[13]?.replace(/`/g, '').trim();
      const couponAmount = parseFloat(couponAmountStr) || 0;

      const transaction: TransactionRecord = {
        reference_no: transactionId, // Using transaction_id as reference
        transaction_id: transactionId,
        transaction_out_id: transactionOutId,
        transaction_card_no: row[1]?.replace(/`/g, '').trim(), // Using card_no from column 1
        transaction_merchant_id: merchantInfo?.merchant_id,
        transaction_merchant_name: merchantInfo?.merchant_name,
        transaction_merchant_vat: merchantInfo?.merchant_vat,
        transaction_time: transactionTime,
        transaction_amount: transactionAmount,
        transaction_refund_id: row[14]?.replace(/`/g, '').trim(),
        transaction_refund_out_id: row[15]?.replace(/`/g, '').trim(),
        transaction_mch_id: row[2]?.replace(/`/g, '').trim(),
        transaction_sub_mch_id: subMchId,
        transaction_trade_type: row[8]?.replace(/`/g, '').trim(),
        transaction_trade_status: tradeStatus,
        transaction_bank_type: row[10]?.replace(/`/g, '').trim(),
        transaction_fee_type: row[11]?.replace(/`/g, '').trim(),
        transaction_coupon_amount: couponAmount,
        transaction_file_name: fileName,
        transaction_file_name_backup: backupPath,
        transaction_channel_type: channelType,
        create_by: currentUser,
        update_by: currentUser
      };

      // Debug logging for transaction record
      console.log(`📝 Transaction Record Debug:`, {
        transaction_id: transactionId,
        raw_trade_status: rawTradeStatus,
        processed_trade_status: tradeStatus,
        transaction_amount: transactionAmount,
        transaction_trade_type: row[8]?.replace(/`/g, '').trim()
      });

      return transaction;

    } catch (error) {
      console.error('❌ Error parsing transaction row:', error);
      throw error;
    }
  }

  /**
   * Get merchant information from sub_mch_id
   */
  private async getMerchantInfo(subMchId: string): Promise<MerchantInfo | null> {
    try {
      if (!subMchId) return null;

      // Query merchant table where merchant_id_wechat matches sub_mch_id
      const result = await executeQuery(
        'SELECT merchant_id, merchant_name, merchant_vat FROM merchant WHERE merchant_id_wechat = $1 AND active = true',
        [subMchId]
      );

      if (result.rows.length > 0) {
        const row = result.rows[0];
        return {
          merchant_id: row.merchant_id,
          merchant_name: row.merchant_name,
          merchant_vat: row.merchant_vat
        };
      }

      console.warn(`⚠️ No merchant found for sub_mch_id: ${subMchId}`);
      return null;

    } catch (error) {
      console.error('❌ Error getting merchant info:', error);
      return null;
    }
  }

  /**
   * Validate refund transaction by checking if corresponding success transaction exists in the same batch
   */
  private validateRefundTransactionInBatch(transactionId: string, allTransactions: TransactionRecord[]): boolean {
    try {
      if (!transactionId) {
        console.log(`⚠️ No transaction_id provided for refund validation`);
        return false;
      }

      // Check if there's a SUCCESS transaction with the same transaction_id in the current batch
      const hasMatchingSuccess = allTransactions.some(transaction =>
        transaction.transaction_id === transactionId &&
        transaction.transaction_trade_status === 'success'
      );

      console.log(`🔍 BATCH REFUND VALIDATION: transaction_id="${transactionId}" -> Found matching SUCCESS in same file: ${hasMatchingSuccess ? 'VALID' : 'INVALID'}`);

      return hasMatchingSuccess;

    } catch (error) {
      console.error('❌ Error validating refund transaction in batch:', error);
      // In case of error, reject the refund (fail-safe approach for batch validation)
      return false;
    }
  }

  /**
   * Determine channel type from filename
   */
  private determineChannelType(fileName: string): string {
    const lowerFileName = fileName.toLowerCase();

    if (lowerFileName.includes('wechat') || lowerFileName.includes('wx')) {
      return 'WeChat';
    } else if (lowerFileName.includes('uni') || lowerFileName.includes('union')) {
      return 'UNIPAY';
    } else if (lowerFileName.includes('alipay')) {
      return 'Alipay';
    }

    // Default based on file structure - if it has WeChat-style columns, assume WeChat
    return 'WeChat';
  }

  /**
   * Save transactions to database using bulk insert - No duplicate handling
   */
  async saveTransactions(
    transactions: TransactionRecord[],
    options?: DuplicateHandlingOptions
  ): Promise<{
    success: boolean;
    savedCount: number;
    duplicateCount: number;
    updatedCount: number;
    errorCount: number;
    errors: string[];
    duplicateDetails: DuplicateLogEntry[];
  }> {

    const result = {
      success: false,
      savedCount: 0,
      duplicateCount: 0,
      updatedCount: 0,
      errorCount: 0,
      errors: [] as string[],
      duplicateDetails: [] as DuplicateLogEntry[]
    };

    try {
      console.log(`💾 BULK INSERT: Saving ${transactions.length} transactions (no duplicate checking)`);

      if (transactions.length === 0) {
        result.success = true;
        console.log('✅ No transactions to save');
        return result;
      }

      // Use large batch sizes for maximum performance since we're not handling duplicates
      const batchSize = 1000;
      const batches = [];
      
      for (let i = 0; i < transactions.length; i += batchSize) {
        batches.push(transactions.slice(i, i + batchSize));
      }

      console.log(`📦 BULK INSERT: Processing ${batches.length} batches of max ${batchSize} records each`);

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        console.log(`🔄 Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} records`);

        try {
          // Build optimized bulk insert query without duplicate handling
          const values: string[] = [];
          const params: any[] = [];
          let paramIndex = 1;

          for (const transaction of batch) {
            const valueClause = `($${paramIndex}, $${paramIndex + 1}, $${paramIndex + 2}, $${paramIndex + 3}, $${paramIndex + 4}, $${paramIndex + 5}, $${paramIndex + 6}, $${paramIndex + 7}, $${paramIndex + 8}, $${paramIndex + 9}, $${paramIndex + 10}, $${paramIndex + 11}, $${paramIndex + 12}, $${paramIndex + 13}, $${paramIndex + 14}, $${paramIndex + 15}, $${paramIndex + 16}, $${paramIndex + 17}, $${paramIndex + 18}, $${paramIndex + 19}, $${paramIndex + 20}, $${paramIndex + 21}, $${paramIndex + 22}, NOW(), NOW())`;
            values.push(valueClause);
            
            params.push(
              transaction.reference_no, transaction.transaction_id, transaction.transaction_out_id,
              transaction.transaction_card_no, transaction.transaction_merchant_id, transaction.transaction_merchant_name,
              transaction.transaction_merchant_vat, transaction.transaction_time, transaction.transaction_amount,
              transaction.transaction_refund_id, transaction.transaction_refund_out_id, transaction.transaction_mch_id,
              transaction.transaction_sub_mch_id, transaction.transaction_trade_type, transaction.transaction_trade_status,
              transaction.transaction_bank_type, transaction.transaction_fee_type, transaction.transaction_coupon_amount,
              transaction.transaction_file_name, transaction.transaction_file_name_backup, transaction.transaction_channel_type,
              transaction.create_by, transaction.update_by
            );
            
            paramIndex += 23; // 23 columns (create_dt and update_dt are auto-generated)
          }

          // Pure bulk insert without any conflict handling
          const bulkInsertQuery = `
            INSERT INTO transaction_e_pos (
              reference_no, transaction_id, transaction_out_id, transaction_card_no,
              transaction_merchant_id, transaction_merchant_name, transaction_merchant_vat,
              transaction_time, transaction_amount, transaction_refund_id, transaction_refund_out_id,
              transaction_mch_id, transaction_sub_mch_id, transaction_trade_type, transaction_trade_status,
              transaction_bank_type, transaction_fee_type, transaction_coupon_amount,
              transaction_file_name, transaction_file_name_backup, transaction_channel_type,
              create_by, update_by, create_dt, update_dt
            ) VALUES ${values.join(', ')}
          `;

          const insertResult = await executeQuery(bulkInsertQuery, params);
          const insertedCount = insertResult.rowCount || 0;

          result.savedCount += insertedCount;

          console.log(`✅ Batch ${batchIndex + 1} complete: ${insertedCount} records inserted`);

        } catch (error) {
          console.error(`❌ Error processing batch ${batchIndex + 1}:`, error);
          
          // If bulk insert fails, record the error but don't fallback to individual inserts
          // This maintains the "no duplicate handling" approach
          result.errorCount += batch.length;
          const errorMsg = `Batch ${batchIndex + 1} failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
          console.error('❌ Batch insert failed:', errorMsg);
        }
      }

      // Success if we inserted any records
      result.success = result.savedCount > 0;
      
      console.log(`✅ BULK INSERT complete: ${result.savedCount} records inserted, ${result.errorCount} errors`);
      console.log(`📊 Total processed: ${result.savedCount} out of ${transactions.length} transactions`);

      return result;

    } catch (error) {
      console.error('❌ Error in BULK INSERT operation:', error);
      result.errors.push(`Database error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }
}
